<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CouponHub - Digital Coupon & Card-Perk Marketplace</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
<span title="Ticket">🎟️</span>
<span>CouponHub</span>
                </div>
                
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Search for coupons, deals, and perks...">
<button class="search-btn" onclick="performSearch()">
    <span title="Search">🔍</span>
</button>
                </div>
                
                <div class="header-actions">
                    <div class="user-actions" id="userActions">
                        <button class="btn btn--outline" onclick="openLoginModal()">Login</button>
                        <button class="btn btn--primary" onclick="openRegisterModal()">Register</button>
                    </div>
                    
                    <div class="user-menu hidden" id="userMenu">
<div class="cart-icon" onclick="toggleCart()">
    <span title="Cart">🛒</span>
    <span class="cart-count" id="cartCount">0</span>
</div>
<div class="user-profile" onclick="toggleUserDropdown()">
    <span title="User">👤</span>
    <span id="userName">User</span>
    <span title="Dropdown">▼</span>
</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- User Dropdown -->
    <div class="user-dropdown hidden" id="userDropdown">
        <a href="#" onclick="showDashboard()">Dashboard</a>
        <a href="#" onclick="showProfile()">Profile</a>
        <a href="#" onclick="showOrders()">My Orders</a>
        <div class="seller-actions hidden" id="sellerActions">
            <a href="#" onclick="showSellerDashboard()">Seller Dashboard</a>
        </div>
        <div class="admin-actions hidden" id="adminActions">
            <a href="#" onclick="showAdminPanel()">Admin Panel</a>
        </div>
        <hr>
        <a href="#" onclick="logout()">Logout</a>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-categories">
                <button class="nav-item active" data-category="all" onclick="filterByCategory('all')">
                    All Categories
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero" id="heroSection">
            <div class="container">
                <div class="hero-content">
                    <h1>India's Largest Digital Coupon & Card-Perk Marketplace</h1>
                    <p>Buy and sell digital coupons, credit card perks, and gift vouchers securely with escrow protection</p>
                    <div class="hero-stats">
                        <div class="stat">
                            <span class="stat-number">10,000+</span>
                            <span class="stat-label">Active Listings</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">50,000+</span>
                            <span class="stat-label">Happy Users</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">₹5Cr+</span>
                            <span class="stat-label">Total Savings</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Dashboard -->
        <div class="dashboard" id="mainDashboard">
            <!-- Featured Listings -->
            <section class="featured-section">
                <div class="container">
                    <h2>Featured Deals</h2>
                    <div class="listings-grid" id="featuredListings">
                        <!-- Listings will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- All Listings -->
            <section class="listings-section">
                <div class="container">
                    <div class="section-header">
                        <h2>All Listings</h2>
                        <div class="filters">
                            <select id="sortBy" onchange="sortListings()">
                                <option value="newest">Newest First</option>
                                <option value="price_low">Price: Low to High</option>
                                <option value="price_high">Price: High to Low</option>
                                <option value="discount">Best Discount</option>
                            </select>
                        </div>
                    </div>
                    <div class="listings-grid" id="allListings">
                        <!-- Listings will be populated by JavaScript -->
                    </div>
                </div>
            </section>
        </div>

        <!-- Seller Dashboard -->
        <div class="seller-dashboard hidden" id="sellerDashboard">
            <div class="container">
                <div class="dashboard-header">
                    <h2>Seller Dashboard</h2>
                    <button class="btn btn--primary" onclick="showCreateListing()">Create New Listing</button>
                </div>
                
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <h3>Total Sales</h3>
                        <div class="stat-value" id="totalSales">₹0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Active Listings</h3>
                        <div class="stat-value" id="activeListings">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Rating</h3>
                        <div class="stat-value" id="sellerRating">0.0</div>
                    </div>
                </div>

                <div class="seller-listings">
                    <h3>My Listings</h3>
                    <div id="sellerListingsGrid" class="listings-grid">
                        <!-- Seller listings will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Panel -->
        <div class="admin-panel hidden" id="adminPanel">
            <div class="container">
                <h2>Admin Panel</h2>
                <div class="admin-tabs">
                    <button class="tab-btn active" onclick="showAdminTab('users')">Users</button>
                    <button class="tab-btn" onclick="showAdminTab('listings')">Listings</button>
                    <button class="tab-btn" onclick="showAdminTab('transactions')">Transactions</button>
                    <button class="tab-btn" onclick="showAdminTab('disputes')">Disputes</button>
                </div>

                <div class="admin-content">
                    <div class="admin-tab active" id="adminUsers">
                        <h3>User Management</h3>
                        <div class="table-container">
                            <table id="usersTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>KYC Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="admin-tab" id="adminListings">
                        <h3>Listing Moderation</h3>
                        <div class="table-container">
                            <table id="listingsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Seller</th>
                                        <th>Status</th>
                                        <th>Price</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="listingsTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="admin-tab" id="adminTransactions">
                        <h3>Transaction Monitoring</h3>
                        <div class="table-container">
                            <table id="transactionsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Buyer</th>
                                        <th>Seller</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Escrow</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionsTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="admin-tab" id="adminDisputes">
                        <h3>Dispute Resolution</h3>
                        <div class="dispute-list" id="disputesList">
                            <p>No active disputes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Shopping Cart Sidebar -->
    <div class="cart-sidebar hidden" id="cartSidebar">
        <div class="cart-header">
            <h3>Shopping Cart</h3>
<button class="close-btn" onclick="toggleCart()">
    <span title="Close">✖️</span>
</button>
        </div>
        <div class="cart-items" id="cartItems">
            <p class="empty-cart">Your cart is empty</p>
        </div>
        <div class="cart-footer">
            <div class="cart-total" id="cartTotal">Total: ₹0</div>
            <button class="btn btn--primary btn--full-width" onclick="proceedToCheckout()">
                Proceed to Checkout
            </button>
        </div>
    </div>

    <!-- Modals -->
    <!-- Login Modal -->
    <div class="modal hidden" id="loginModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Login to CouponHub</h3>
<button class="close-btn" onclick="closeModal('loginModal')">
    <span title="Close">✖️</span>
</button>
            </div>
            <form id="loginForm" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" id="loginPassword" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn--primary btn--full-width">Login</button>
                </div>
            </form>
            <p class="modal-footer-text">
                Don't have an account? <a href="#" onclick="switchToRegister()">Register here</a>
            </p>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal hidden" id="registerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Join CouponHub</h3>
<button class="close-btn" onclick="closeModal('registerModal')">
    <span title="Close">✖️</span>
</button>
            </div>
            <form id="registerForm" onsubmit="handleRegister(event)">
                <div class="form-group">
                    <label class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="registerName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" id="registerEmail" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Phone</label>
                    <input type="tel" class="form-control" id="registerPhone" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" id="registerPassword" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Role</label>
                    <select class="form-control" id="registerRole" required>
                        <option value="buyer">Buyer</option>
                        <option value="seller">Seller</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn--primary btn--full-width">Register</button>
                </div>
            </form>
            <p class="modal-footer-text">
                Already have an account? <a href="#" onclick="switchToLogin()">Login here</a>
            </p>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal hidden" id="productModal">
        <div class="modal-content product-modal-content">
            <div class="modal-header">
                <h3 id="productTitle">Product Details</h3>
<button class="close-btn" onclick="closeModal('productModal')">
    <span title="Close">✖️</span>
</button>
            </div>
            <div class="product-detail-content" id="productDetailContent">
                <!-- Product details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Create Listing Modal -->
    <div class="modal hidden" id="createListingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Listing</h3>
<button class="close-btn" onclick="closeModal('createListingModal')">
    <span title="Close">✖️</span>
</button>
            </div>
            <form id="createListingForm" onsubmit="handleCreateListing(event)">
                <div class="form-group">
                    <label class="form-label">Title</label>
                    <input type="text" class="form-control" id="listingTitle" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <textarea class="form-control" id="listingDescription" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Category</label>
                    <select class="form-control" id="listingCategory" required>
                        <option value="">Select Category</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Brand</label>
                    <input type="text" class="form-control" id="listingBrand" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Original Price (₹)</label>
                    <input type="number" class="form-control" id="listingOriginalPrice" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Selling Price (₹)</label>
                    <input type="number" class="form-control" id="listingSellingPrice" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Expiry Date</label>
                    <input type="date" class="form-control" id="listingExpiry" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn--primary btn--full-width">Create Listing</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div class="modal hidden" id="checkoutModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Secure Checkout</h3>
<button class="close-btn" onclick="closeModal('checkoutModal')">
    <span title="Close">✖️</span>
</button>
            </div>
            <div class="checkout-content">
                <div class="order-summary" id="orderSummary">
                    <!-- Order summary will be populated here -->
                </div>
                <form id="checkoutForm" onsubmit="handleCheckout(event)">
                    <div class="form-group">
                        <label class="form-label">Payment Method</label>
                        <select class="form-control" id="paymentMethod" required>
                            <option value="razorpay">Razorpay (UPI/Cards/Net Banking)</option>
                            <option value="stripe">Stripe (International Cards)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn--primary btn--full-width">
                            Pay Securely with Escrow Protection
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer">
    </div>

    <script src="app.js"></script>
</body>
</html>
