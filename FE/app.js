// CouponHub Marketplace Application
class CouponHubApp {
    constructor() {
        this.currentUser = null;
        this.cart = [];
        this.currentView = 'main';
        this.searchQuery = '';
        this.selectedCategory = 'all';
        
        // Initialize application data
        this.initializeData();
        
        // Initialize the application
        this.init();
    }

    initializeData() {
        // Mock data for the application
        this.users = [
            {
                id: "1",
                email: "<EMAIL>",
                name: "John Buyer",
                phone: "+91-9876543210",
                role: "buyer",
                kycStatus: "verified",
                joinDate: "2024-01-15",
                rating: 4.8,
                totalTransactions: 25
            },
            {
                id: "2", 
                email: "<EMAIL>",
                name: "<PERSON>",
                phone: "+91-9876543211",
                role: "seller",
                kycStatus: "verified",
                joinDate: "2023-12-01",
                rating: 4.9,
                totalSales: 150,
                businessName: "Digital Deals Hub"
            },
            {
                id: "3",
                email: "<EMAIL>", 
                name: "Admin User",
                role: "admin",
                permissions: ["user_management", "listing_moderation", "dispute_resolution"]
            }
        ];

        this.categories = [
            {id: "1", name: "Digital Coupons", icon: "🎫", description: "E-commerce and service coupons"},
            {id: "2", name: "Credit Card Perks", icon: "💳", description: "Bank card offers and benefits"}, 
            {id: "3", name: "Gift Vouchers", icon: "🎁", description: "Brand and store gift cards"},
            {id: "4", name: "Food & Dining", icon: "🍽️", description: "Restaurant and food delivery deals"},
            {id: "5", name: "Electronics", icon: "📱", description: "Tech and gadget discounts"},
            {id: "6", name: "Fashion", icon: "👕", description: "Clothing and accessories deals"}
        ];

        this.listings = [
            {
                id: "1",
                title: "Flipkart 20% Off Electronics Coupon",
                description: "Valid on electronics worth ₹5000+. One-time use only. Valid till March 2025.",
                category: "Digital Coupons",
                originalPrice: 1000,
                sellingPrice: 750,
                discountPercent: 20,
                brand: "Flipkart",
                sellerId: "2",
                status: "active",
                expiry: "2025-03-31",
                image: "favicon.svg",
                verified: true,
                tags: ["electronics", "online-shopping"],
                createdAt: "2024-12-01T10:00:00Z"
            },
            {
                id: "2", 
                title: "HDFC Credit Card - Movie Ticket BOGO",
                description: "Buy 1 Get 1 free movie tickets on BookMyShow. Valid on HDFC credit cards only.",
                category: "Credit Card Perks",
                originalPrice: 500,
                sellingPrice: 200, 
                discountPercent: 50,
                brand: "HDFC Bank",
                sellerId: "2",
                status: "active",
                expiry: "2025-02-28",
                image: "favicon.svg",
                requiresOTP: true,
                cardType: "HDFC Credit Card",
                verified: true,
                tags: ["movies", "entertainment"]
            },
            {
                id: "3",
                title: "Amazon Gift Card ₹1000",
                description: "Amazon gift voucher worth ₹1000. Valid for all products on Amazon.in",
                category: "Gift Vouchers", 
                originalPrice: 1000,
                sellingPrice: 950,
                discountPercent: 5,
                brand: "Amazon",
                sellerId: "2", 
                status: "active",
                expiry: "2025-12-31",
                image: "favicon.svg",
                verified: true,
                tags: ["gift-card", "amazon"]
            },
            {
                id: "4",
                title: "Zomato 30% Off Food Orders",
                description: "30% off on food orders above ₹300. Valid for first-time users only.",
                category: "Food & Dining",
                originalPrice: 200,
                sellingPrice: 150,
                discountPercent: 30, 
                brand: "Zomato",
                sellerId: "2",
                status: "active",
                expiry: "2025-01-31",
                image: "favicon.svg",
                verified: true,
                tags: ["food-delivery", "dining"]
            },
            {
                id: "5",
                title: "SBI Credit Card - Fuel Cashback",
                description: "Get 5% cashback on fuel purchases up to ₹500 per month. Valid at all petrol pumps.",
                category: "Credit Card Perks",
                originalPrice: 300,
                sellingPrice: 100,
                discountPercent: 67,
                brand: "SBI Bank",
                sellerId: "2",
                status: "active",
                expiry: "2025-06-30",
                image: "favicon.svg",
                requiresOTP: true,
                cardType: "SBI Credit Card",
                verified: true,
                tags: ["fuel", "cashback"]
            },
            {
                id: "6",
                title: "Myntra Fashion Sale Coupon",
                description: "Flat 40% off on fashion items. Minimum purchase ₹2000. One-time use coupon.",
                category: "Fashion",
                originalPrice: 800,
                sellingPrice: 600,
                discountPercent: 40,
                brand: "Myntra",
                sellerId: "2",
                status: "active",
                expiry: "2025-04-15",
                image: "favicon.svg",
                verified: true,
                tags: ["fashion", "clothing"]
            }
        ];

        this.transactions = [
            {
                id: "T001",
                buyerId: "1",
                sellerId: "2", 
                listingId: "1",
                amount: 750,
                status: "completed",
                escrowStatus: "released",
                createdAt: "2024-12-10T14:30:00Z",
                completedAt: "2024-12-11T09:15:00Z",
                paymentMethod: "razorpay",
                platformFee: 50,
                sellerAmount: 700
            },
            {
                id: "T002", 
                buyerId: "1",
                sellerId: "2",
                listingId: "2", 
                amount: 200,
                status: "in_progress",
                escrowStatus: "holding",
                createdAt: "2024-12-12T11:20:00Z",
                paymentMethod: "razorpay",
                otpRequired: true,
                otpStatus: "pending"
            }
        ];

        this.reviews = [
            {
                id: "R001",
                transactionId: "T001",
                reviewerId: "1", 
                revieweeId: "2",
                rating: 5,
                comment: "Great seller! Coupon worked perfectly. Quick delivery.",
                createdAt: "2024-12-11T10:00:00Z"
            }
        ];

        // Load saved data from localStorage
        this.loadUserSession();
        this.loadCart();
    }

    init() {
        this.setupEventListeners();
        this.renderCategories();
        this.renderListings();
        this.updateUIForUser();
        this.renderPopulateCategoryDropdown();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.filterAndRenderListings();
            });
            
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }

        // Search button
        const searchBtn = document.querySelector('.search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }

        // User profile dropdown
        const userProfile = document.querySelector('.user-profile');
        if (userProfile) {
            userProfile.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleUserDropdown();
            });
        }

        // Cart icon click
        const cartIcon = document.querySelector('.cart-icon');
        if (cartIcon) {
            cartIcon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleCart();
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-profile') && !e.target.closest('.user-dropdown')) {
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
            }

            if (!e.target.closest('.cart-sidebar') && !e.target.closest('.cart-icon')) {
                const cartSidebar = document.getElementById('cartSidebar');
                if (cartSidebar) {
                    cartSidebar.classList.remove('active');
                }
            }
        });

        // Modal background click to close
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal.id);
                }
            });
        });

        // Category navigation items
        setTimeout(() => {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    const category = e.target.getAttribute('data-category');
                    if (category) {
                        this.filterByCategory(category);
                    }
                });
            });
        }, 100);
    }

    // Authentication Methods
    saveUserSession() {
        if (this.currentUser) {
            localStorage.setItem('couponhub_user', JSON.stringify(this.currentUser));
        } else {
            localStorage.removeItem('couponhub_user');
        }
    }

    loadUserSession() {
        const saved = localStorage.getItem('couponhub_user');
        if (saved) {
            try {
                this.currentUser = JSON.parse(saved);
            } catch (e) {
                localStorage.removeItem('couponhub_user');
            }
        }
    }

    updateUIForUser() {
        const userActions = document.getElementById('userActions');
        const userMenu = document.getElementById('userMenu');
        const sellerActions = document.getElementById('sellerActions');
        const adminActions = document.getElementById('adminActions');

        if (this.currentUser) {
            if (userActions) userActions.classList.add('hidden');
            if (userMenu) userMenu.classList.remove('hidden');
            
            const userName = document.getElementById('userName');
            if (userName) {
                userName.textContent = this.currentUser.name.split(' ')[0];
            }

            // Show role-specific actions
            if (this.currentUser.role === 'seller' || this.currentUser.role === 'admin') {
                if (sellerActions) sellerActions.classList.remove('hidden');
            } else {
                if (sellerActions) sellerActions.classList.add('hidden');
            }

            if (this.currentUser.role === 'admin') {
                if (adminActions) adminActions.classList.remove('hidden');
            } else {
                if (adminActions) adminActions.classList.add('hidden');
            }
        } else {
            if (userActions) userActions.classList.remove('hidden');
            if (userMenu) userMenu.classList.add('hidden');
        }

        // Re-setup user profile click handler after UI update
        setTimeout(() => {
            const userProfile = document.querySelector('.user-profile');
            if (userProfile && this.currentUser) {
                // Remove any existing listeners and add new one
                userProfile.removeEventListener('click', this.handleUserProfileClick);
                userProfile.addEventListener('click', this.handleUserProfileClick.bind(this));
            }
        }, 100);
    }

    handleUserProfileClick(e) {
        e.preventDefault();
        e.stopPropagation();
        this.toggleUserDropdown();
    }

    // Cart Methods
    saveCart() {
        localStorage.setItem('couponhub_cart', JSON.stringify(this.cart));
    }

    loadCart() {
        const saved = localStorage.getItem('couponhub_cart');
        if (saved) {
            try {
                this.cart = JSON.parse(saved);
                this.updateCartUI();
            } catch (e) {
                this.cart = [];
                localStorage.removeItem('couponhub_cart');
            }
        }
    }

    addToCart(listingId) {
        if (!this.currentUser) {
            this.showToast('Please login to add items to cart', 'error');
            this.openModal('loginModal');
            return;
        }

        const listing = this.listings.find(l => l.id === listingId);
        if (!listing) return;

        // Check if item already in cart
        const existing = this.cart.find(item => item.id === listingId);
        if (existing) {
            this.showToast('Item already in cart', 'warning');
            return;
        }

        this.cart.push({
            id: listingId,
            title: listing.title,
            price: listing.sellingPrice,
            image: listing.image,
            brand: listing.brand
        });

        this.saveCart();
        this.updateCartUI();
        this.showToast('Added to cart successfully', 'success');
    }

    removeFromCart(listingId) {
        this.cart = this.cart.filter(item => item.id !== listingId);
        this.saveCart();
        this.updateCartUI();
        this.showToast('Removed from cart', 'success');
    }

    updateCartUI() {
        const cartCount = document.getElementById('cartCount');
        const cartItems = document.getElementById('cartItems');
        const cartTotal = document.getElementById('cartTotal');

        if (cartCount) {
            cartCount.textContent = this.cart.length;
        }

        if (cartItems && cartTotal) {
            if (this.cart.length === 0) {
                cartItems.innerHTML = '<p class="empty-cart">Your cart is empty</p>';
                cartTotal.textContent = 'Total: ₹0';
            } else {
                const total = this.cart.reduce((sum, item) => sum + item.price, 0);
                cartTotal.textContent = `Total: ₹${total.toLocaleString()}`;

                cartItems.innerHTML = this.cart.map(item => `
                    <div class="cart-item">
                        <div class="cart-item-image">
                            <img src="${item.image}" alt="${item.title}" onerror="this.style.display='none'">
                        </div>
                        <div class="cart-item-details">
                            <div class="cart-item-title">${item.title}</div>
                            <div class="cart-item-price">₹${item.price.toLocaleString()}</div>
                            <button class="btn btn--sm" onclick="app.removeFromCart('${item.id}')">Remove</button>
                        </div>
                    </div>
                `).join('');
            }
        }
    }

    // UI Methods
    renderCategories() {
        const container = document.querySelector('.nav-categories');
        if (!container) return;
        
        // Clear existing categories except "All Categories"
        const allCategoriesBtn = container.querySelector('[data-category="all"]');
        
        this.categories.forEach(category => {
            const button = document.createElement('button');
            button.className = 'nav-item';
            button.textContent = `${category.icon} ${category.name}`;
            button.setAttribute('data-category', category.name);
            button.addEventListener('click', () => this.filterByCategory(category.name));
            container.appendChild(button);
        });
    }

    renderPopulateCategoryDropdown() {
        const select = document.getElementById('listingCategory');
        if (select) {
            select.innerHTML = '<option value="">Select Category</option>';
            this.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.name;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }
    }

    renderListings() {
        this.renderFeaturedListings();
        this.renderAllListings();
    }

    renderFeaturedListings() {
        const container = document.getElementById('featuredListings');
        if (!container) return;
        
        const featured = this.listings.slice(0, 3); // First 3 as featured
        container.innerHTML = featured.map(listing => this.createListingCard(listing)).join('');
    }

    renderAllListings() {
        const container = document.getElementById('allListings');
        if (!container) return;
        
        let filteredListings = this.getFilteredListings();
        
        if (filteredListings.length === 0) {
            container.innerHTML = '<div class="text-center">No listings found matching your criteria.</div>';
            return;
        }
        
        container.innerHTML = filteredListings.map(listing => this.createListingCard(listing)).join('');
    }

    getFilteredListings() {
        let filtered = [...this.listings];
        
        // Filter by category
        if (this.selectedCategory && this.selectedCategory !== 'all') {
            filtered = filtered.filter(listing => listing.category === this.selectedCategory);
        }
        
        // Filter by search query
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(listing => 
                listing.title.toLowerCase().includes(query) ||
                listing.brand.toLowerCase().includes(query) ||
                listing.description.toLowerCase().includes(query)
            );
        }
        
        return filtered;
    }

    createListingCard(listing) {
        const isExpired = new Date(listing.expiry) < new Date();
        const statusBadge = listing.verified ? '<div class="listing-badge">Verified</div>' : '';
        
        return `
            <div class="listing-card" onclick="app.showProductDetail('${listing.id}')">
                <div class="listing-image">
                    ${statusBadge}
                    <img src="${listing.image}" alt="${listing.title}" onerror="this.style.display='none'">
                </div>
                <div class="listing-content">
                    <h3 class="listing-title">${listing.title}</h3>
                    <p class="listing-description">${listing.description}</p>
                    <div class="listing-meta">
                        <span class="listing-brand">${listing.brand}</span>
                        <span class="listing-expiry ${isExpired ? 'text-error' : ''}">
                            Expires: ${new Date(listing.expiry).toLocaleDateString()}
                        </span>
                    </div>
                    <div class="listing-pricing">
                        <div class="price-group">
                            <span class="current-price">₹${listing.sellingPrice.toLocaleString()}</span>
                            <span class="original-price">₹${listing.originalPrice.toLocaleString()}</span>
                        </div>
                        <span class="discount-badge">${listing.discountPercent}% OFF</span>
                    </div>
                    <div class="listing-actions" onclick="event.stopPropagation()">
                        <button class="btn-add-cart" onclick="app.addToCart('${listing.id}')">
                            <i class="fas fa-cart-plus"></i> Add to Cart
                        </button>
                        <button class="btn btn--primary btn-buy-now" onclick="app.buyNow('${listing.id}')">
                            Buy Now
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Filter and Search Methods
    filterByCategory(category) {
        this.selectedCategory = category;
        
        // Update active nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-category="${category}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        this.filterAndRenderListings();
    }

    filterAndRenderListings() {
        this.renderAllListings();
    }

    sortListings() {
        const sortBy = document.getElementById('sortBy')?.value;
        if (!sortBy) return;
        
        let listings = this.getFilteredListings();
        
        switch (sortBy) {
            case 'price_low':
                listings.sort((a, b) => a.sellingPrice - b.sellingPrice);
                break;
            case 'price_high':
                listings.sort((a, b) => b.sellingPrice - a.sellingPrice);
                break;
            case 'discount':
                listings.sort((a, b) => b.discountPercent - a.discountPercent);
                break;
            case 'newest':
            default:
                listings.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
        }
        
        const container = document.getElementById('allListings');
        if (container) {
            container.innerHTML = listings.map(listing => this.createListingCard(listing)).join('');
        }
    }

    // Modal Methods
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    // Product Detail Methods
    showProductDetail(listingId) {
        const listing = this.listings.find(l => l.id === listingId);
        if (!listing) return;

        const seller = this.users.find(u => u.id === listing.sellerId);
        const isExpired = new Date(listing.expiry) < new Date();

        const productTitle = document.getElementById('productTitle');
        const productDetailContent = document.getElementById('productDetailContent');
        
        if (productTitle) {
            productTitle.textContent = listing.title;
        }
        
        if (productDetailContent) {
            productDetailContent.innerHTML = `
                <div class="product-detail-grid">
                    <div class="product-detail-image">
                        <img src="${listing.image}" alt="${listing.title}" onerror="this.style.display='none'">
                    </div>
                    <div class="product-detail-info">
                        <h2>${listing.title}</h2>
                        <p>${listing.description}</p>
                        
                        <div class="product-detail-meta">
                            <div class="meta-item">Brand: ${listing.brand}</div>
                            <div class="meta-item">Category: ${listing.category}</div>
                            <div class="meta-item ${isExpired ? 'text-error' : ''}">
                                Expires: ${new Date(listing.expiry).toLocaleDateString()}
                            </div>
                            ${listing.verified ? '<div class="meta-item text-success">✓ Verified</div>' : ''}
                            ${listing.requiresOTP ? '<div class="meta-item text-warning">⚠ Requires OTP</div>' : ''}
                        </div>

                        <div class="product-pricing">
                            <div class="current-price">₹${listing.sellingPrice.toLocaleString()}</div>
                            <div class="original-price">₹${listing.originalPrice.toLocaleString()}</div>
                            <div class="discount-badge">${listing.discountPercent}% OFF</div>
                        </div>

                        <div class="seller-info">
                            <h4>Seller Information</h4>
                            <p>Sold by: ${seller?.name || 'Unknown'}</p>
                            <p>Rating: ${seller?.rating || 'N/A'} ⭐</p>
                            <p>KYC: ${seller?.kycStatus === 'verified' ? '✓ Verified' : '⚠ Pending'}</p>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn--outline btn--full-width" onclick="app.addToCart('${listing.id}')">
                                <i class="fas fa-cart-plus"></i> Add to Cart
                            </button>
                            <button class="btn btn--primary btn--full-width" onclick="app.buyNow('${listing.id}')">
                                Buy Now
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        this.openModal('productModal');
    }

    // Purchase Methods
    buyNow(listingId) {
        if (!this.currentUser) {
            this.showToast('Please login to make purchases', 'error');
            this.openModal('loginModal');
            return;
        }

        const listing = this.listings.find(l => l.id === listingId);
        if (!listing) return;

        // Add to cart temporarily for checkout
        this.addToCart(listingId);
        this.proceedToCheckout();
    }

    proceedToCheckout() {
        if (this.cart.length === 0) {
            this.showToast('Your cart is empty', 'warning');
            return;
        }

        const total = this.cart.reduce((sum, item) => sum + item.price, 0);
        const platformFee = Math.round(total * 0.05); // 5% platform fee
        const finalTotal = total + platformFee;

        const orderSummary = document.getElementById('orderSummary');
        if (orderSummary) {
            orderSummary.innerHTML = `
                <h4>Order Summary</h4>
                ${this.cart.map(item => `
                    <div class="order-item">
                        <span>${item.title}</span>
                        <span>₹${item.price.toLocaleString()}</span>
                    </div>
                `).join('')}
                <div class="order-item">
                    <span>Platform Fee (5%)</span>
                    <span>₹${platformFee.toLocaleString()}</span>
                </div>
                <div class="order-item order-total">
                    <span>Total Amount</span>
                    <span>₹${finalTotal.toLocaleString()}</span>
                </div>
                <div class="escrow-notice" style="margin-top: 1rem; padding: 1rem; background: var(--color-bg-3); border-radius: var(--radius-base); font-size: var(--font-size-sm);">
                    <i class="fas fa-shield-alt"></i> Your payment will be held in secure escrow until you confirm receipt of the items.
                </div>
            `;
        }

        this.toggleCart(); // Close cart sidebar
        this.openModal('checkoutModal');
    }

    handleCheckout(event) {
        event.preventDefault();
        
        if (!this.currentUser) {
            this.showToast('Please login to complete purchase', 'error');
            return;
        }

        const paymentMethod = document.getElementById('paymentMethod')?.value;
        const total = this.cart.reduce((sum, item) => sum + item.price, 0);
        const platformFee = Math.round(total * 0.05);
        const finalTotal = total + platformFee;

        // Simulate payment processing
        this.showToast('Processing payment...', 'info');
        
        setTimeout(() => {
            // Create mock transaction
            const transactionId = 'T' + Date.now();
            const transaction = {
                id: transactionId,
                buyerId: this.currentUser.id,
                items: [...this.cart],
                amount: finalTotal,
                status: 'completed',
                escrowStatus: 'holding',
                createdAt: new Date().toISOString(),
                paymentMethod: paymentMethod,
                platformFee: platformFee
            };

            this.transactions.push(transaction);
            
            // Clear cart
            this.cart = [];
            this.saveCart();
            this.updateCartUI();
            
            // Close modal
            this.closeModal('checkoutModal');
            
            this.showToast('Payment successful! Items will be delivered shortly.', 'success');
            this.showOrderConfirmation(transaction);
        }, 2000);
    }

    showOrderConfirmation(transaction) {
        // In a real app, this would redirect to order details page
        this.showToast(`Order ${transaction.id} created. Check your email for details.`, 'success');
    }

    // Dashboard Methods
    showDashboard() {
        this.currentView = 'main';
        this.hideAllViews();
        const mainDashboard = document.getElementById('mainDashboard');
        const heroSection = document.getElementById('heroSection');
        if (mainDashboard) mainDashboard.classList.remove('hidden');
        if (heroSection) heroSection.classList.remove('hidden');
        
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) userDropdown.classList.add('hidden');
    }

    showSellerDashboard() {
        if (this.currentUser && (this.currentUser.role === 'seller' || this.currentUser.role === 'admin')) {
            this.currentView = 'seller';
            this.hideAllViews();
            const sellerDashboard = document.getElementById('sellerDashboard');
            if (sellerDashboard) {
                sellerDashboard.classList.remove('hidden');
                this.renderSellerDashboard();
            }
        }
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) userDropdown.classList.add('hidden');
    }

    showAdminPanel() {
        if (this.currentUser && this.currentUser.role === 'admin') {
            this.currentView = 'admin';
            this.hideAllViews();
            const adminPanel = document.getElementById('adminPanel');
            if (adminPanel) {
                adminPanel.classList.remove('hidden');
                this.renderAdminPanel();
            }
        }
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) userDropdown.classList.add('hidden');
    }

    hideAllViews() {
        const mainDashboard = document.getElementById('mainDashboard');
        const sellerDashboard = document.getElementById('sellerDashboard');
        const adminPanel = document.getElementById('adminPanel');
        const heroSection = document.getElementById('heroSection');
        
        if (mainDashboard) mainDashboard.classList.add('hidden');
        if (sellerDashboard) sellerDashboard.classList.add('hidden');
        if (adminPanel) adminPanel.classList.add('hidden');
        if (heroSection) heroSection.classList.add('hidden');
    }

    renderSellerDashboard() {
        if (!this.currentUser) return;
        
        const sellerListings = this.listings.filter(l => l.sellerId === this.currentUser.id);
        const sellerTransactions = this.transactions.filter(t => t.sellerId === this.currentUser.id);
        const totalSales = sellerTransactions.reduce((sum, t) => sum + (t.sellerAmount || t.amount * 0.95), 0);
        
        const totalSalesEl = document.getElementById('totalSales');
        const activeListingsEl = document.getElementById('activeListings');
        const sellerRatingEl = document.getElementById('sellerRating');
        
        if (totalSalesEl) totalSalesEl.textContent = `₹${totalSales.toLocaleString()}`;
        if (activeListingsEl) activeListingsEl.textContent = sellerListings.filter(l => l.status === 'active').length;
        if (sellerRatingEl) sellerRatingEl.textContent = this.currentUser.rating || '5.0';

        const container = document.getElementById('sellerListingsGrid');
        if (container) {
            if (sellerListings.length === 0) {
                container.innerHTML = '<div class="text-center">No listings found. <button class="btn btn--primary" onclick="app.showCreateListing()">Create your first listing</button></div>';
            } else {
                container.innerHTML = sellerListings.map(listing => `
                    <div class="listing-card">
                        <div class="listing-image">
                            <img src="${listing.image}" alt="${listing.title}" onerror="this.style.display='none'">
                        </div>
                        <div class="listing-content">
                            <h3 class="listing-title">${listing.title}</h3>
                            <div class="listing-meta">
                                <span class="status ${listing.status}">${listing.status.toUpperCase()}</span>
                                <span>₹${listing.sellingPrice.toLocaleString()}</span>
                            </div>
                            <div class="listing-actions">
                                <button class="btn btn--sm btn--outline" onclick="app.editListing('${listing.id}')">Edit</button>
                                <button class="btn btn--sm text-error" onclick="app.deleteListing('${listing.id}')">Delete</button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }
    }

    renderAdminPanel() {
        this.renderUsersTable();
        this.renderListingsTable();
        this.renderTransactionsTable();
    }

    renderUsersTable() {
        const tbody = document.getElementById('usersTableBody');
        if (tbody) {
            tbody.innerHTML = this.users.map(user => `
                <tr>
                    <td>${user.id}</td>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>${user.role}</td>
                    <td><span class="status ${user.kycStatus}">${user.kycStatus}</span></td>
                    <td>
                        <button class="btn btn--sm" onclick="app.viewUser('${user.id}')">View</button>
                        <button class="btn btn--sm btn--outline" onclick="app.suspendUser('${user.id}')">Suspend</button>
                    </td>
                </tr>
            `).join('');
        }
    }

    renderListingsTable() {
        const tbody = document.getElementById('listingsTableBody');
        if (tbody) {
            tbody.innerHTML = this.listings.map(listing => {
                const seller = this.users.find(u => u.id === listing.sellerId);
                return `
                    <tr>
                        <td>${listing.id}</td>
                        <td>${listing.title}</td>
                        <td>${seller?.name || 'Unknown'}</td>
                        <td><span class="status ${listing.status}">${listing.status}</span></td>
                        <td>₹${listing.sellingPrice.toLocaleString()}</td>
                        <td>
                            <button class="btn btn--sm" onclick="app.approveListing('${listing.id}')">Approve</button>
                            <button class="btn btn--sm text-error" onclick="app.rejectListing('${listing.id}')">Reject</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }
    }

    renderTransactionsTable() {
        const tbody = document.getElementById('transactionsTableBody');
        if (tbody) {
            tbody.innerHTML = this.transactions.map(transaction => {
                const buyer = this.users.find(u => u.id === transaction.buyerId);
                const seller = this.users.find(u => u.id === transaction.sellerId);
                return `
                    <tr>
                        <td>${transaction.id}</td>
                        <td>${buyer?.name || 'Unknown'}</td>
                        <td>${seller?.name || 'Unknown'}</td>
                        <td>₹${transaction.amount.toLocaleString()}</td>
                        <td><span class="status ${transaction.status}">${transaction.status}</span></td>
                        <td><span class="status ${transaction.escrowStatus}">${transaction.escrowStatus}</span></td>
                        <td>
                            <button class="btn btn--sm" onclick="app.viewTransaction('${transaction.id}')">View</button>
                            <button class="btn btn--sm btn--outline" onclick="app.releaseEscrow('${transaction.id}')">Release</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }
    }

    // Admin Actions
    showAdminTab(tabName) {
        // Update active tab
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        const activeTabBtn = document.querySelector(`button[onclick*="${tabName}"]`);
        if (activeTabBtn) activeTabBtn.classList.add('active');
        
        // Show corresponding content
        document.querySelectorAll('.admin-tab').forEach(tab => tab.classList.remove('active'));
        const activeTab = document.getElementById(`admin${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
        if (activeTab) activeTab.classList.add('active');
    }

    // Listing Management
    showCreateListing() {
        this.openModal('createListingModal');
    }

    handleCreateListing(event) {
        event.preventDefault();
        
        if (!this.currentUser || (this.currentUser.role !== 'seller' && this.currentUser.role !== 'admin')) {
            this.showToast('Only sellers can create listings', 'error');
            return;
        }

        const newListing = {
            id: Date.now().toString(),
            title: document.getElementById('listingTitle')?.value || '',
            description: document.getElementById('listingDescription')?.value || '',
            category: document.getElementById('listingCategory')?.value || '',
            brand: document.getElementById('listingBrand')?.value || '',
            originalPrice: parseInt(document.getElementById('listingOriginalPrice')?.value || '0'),
            sellingPrice: parseInt(document.getElementById('listingSellingPrice')?.value || '0'),
            expiry: document.getElementById('listingExpiry')?.value || '',
            sellerId: this.currentUser.id,
            status: 'active',
            verified: false,
            tags: [],
            createdAt: new Date().toISOString(),
            image: "favicon.svg"
        };

        // Calculate discount percentage
        newListing.discountPercent = Math.round(((newListing.originalPrice - newListing.sellingPrice) / newListing.originalPrice) * 100);

        this.listings.push(newListing);
        this.closeModal('createListingModal');
        this.showToast('Listing created successfully!', 'success');
        
        // Refresh current view
        if (this.currentView === 'seller') {
            this.renderSellerDashboard();
        } else {
            this.renderListings();
        }
        
        // Reset form
        const form = document.getElementById('createListingForm');
        if (form) form.reset();
    }

    // Utility Methods
    toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
            dropdown.classList.toggle('hidden');
        }
    }

    toggleCart() {
        const sidebar = document.getElementById('cartSidebar');
        if (sidebar) {
            if (sidebar.classList.contains('hidden')) {
                sidebar.classList.remove('hidden');
                sidebar.classList.add('active');
            } else if (sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
                sidebar.classList.add('hidden');
            } else {
                sidebar.classList.add('active');
            }
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        container.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove toast after 4 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (container.contains(toast)) {
                    container.removeChild(toast);
                }
            }, 300);
        }, 4000);
    }

    logout() {
        this.currentUser = null;
        this.cart = [];
        this.saveUserSession();
        this.saveCart();
        this.updateCartUI();
        this.updateUIForUser();
        this.showDashboard();
        this.showToast('Logged out successfully', 'success');
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) userDropdown.classList.add('hidden');
    }

    // Search functionality
    performSearch() {
        const query = document.getElementById('searchInput')?.value || '';
        this.searchQuery = query;
        this.filterAndRenderListings();
        if (query) {
            const results = this.getFilteredListings();
            this.showToast(`Found ${results.length} results for "${query}"`, 'info');
        }
    }

    // Placeholder methods for admin/seller actions
    editListing(id) { this.showToast('Edit listing feature coming soon!', 'info'); }
    deleteListing(id) { this.showToast('Delete listing feature coming soon!', 'info'); }
    viewUser(id) { this.showToast('View user feature coming soon!', 'info'); }
    suspendUser(id) { this.showToast('Suspend user feature coming soon!', 'info'); }
    approveListing(id) { this.showToast('Listing approved!', 'success'); }
    rejectListing(id) { this.showToast('Listing rejected!', 'warning'); }
    viewTransaction(id) { this.showToast('View transaction feature coming soon!', 'info'); }
    releaseEscrow(id) { this.showToast('Escrow released!', 'success'); }
}

// Global event handler functions
window.openLoginModal = function() {
    if (window.app) window.app.openModal('loginModal');
};

window.openRegisterModal = function() {
    if (window.app) window.app.openModal('registerModal');
};

window.closeModal = function(modalId) {
    if (window.app) window.app.closeModal(modalId);
};

window.switchToRegister = function() {
    if (window.app) {
        window.app.closeModal('loginModal');
        window.app.openModal('registerModal');
    }
};

window.switchToLogin = function() {
    if (window.app) {
        window.app.closeModal('registerModal');
        window.app.openModal('loginModal');
    }
};

window.handleLogin = function(event) {
    event.preventDefault();
    
    const email = document.getElementById('loginEmail')?.value;
    const password = document.getElementById('loginPassword')?.value;
    
    if (!email || !password) {
        window.app?.showToast('Please fill in all fields', 'error');
        return;
    }
    
    // Mock authentication - find user by email
    const user = window.app?.users.find(u => u.email === email);
    
    if (user && window.app) {
        window.app.currentUser = user;
        window.app.saveUserSession();
        window.app.updateUIForUser();
        window.app.closeModal('loginModal');
        window.app.showToast(`Welcome back, ${user.name}!`, 'success');
        const form = document.getElementById('loginForm');
        if (form) form.reset();
    } else {
        window.app?.showToast('Invalid email or password', 'error');
    }
};

window.handleRegister = function(event) {
    event.preventDefault();
    
    const name = document.getElementById('registerName')?.value;
    const email = document.getElementById('registerEmail')?.value;
    const phone = document.getElementById('registerPhone')?.value;
    const role = document.getElementById('registerRole')?.value;
    
    if (!name || !email || !phone || !role) {
        window.app?.showToast('Please fill in all fields', 'error');
        return;
    }
    
    // Check if user already exists
    if (window.app?.users.find(u => u.email === email)) {
        window.app.showToast('User with this email already exists', 'error');
        return;
    }
    
    // Create new user
    const newUser = {
        id: Date.now().toString(),
        email,
        name,
        phone,
        role,
        kycStatus: 'pending',
        joinDate: new Date().toISOString().split('T')[0],
        rating: 5.0,
        totalTransactions: 0
    };
    
    if (window.app) {
        window.app.users.push(newUser);
        window.app.currentUser = newUser;
        window.app.saveUserSession();
        window.app.updateUIForUser();
        window.app.closeModal('registerModal');
        window.app.showToast(`Welcome to CouponHub, ${name}!`, 'success');
        const form = document.getElementById('registerForm');
        if (form) form.reset();
    }
};

// Other global functions
window.performSearch = function() { window.app?.performSearch(); };
window.filterByCategory = function(category) { window.app?.filterByCategory(category); };
window.sortListings = function() { window.app?.sortListings(); };
window.showDashboard = function() { window.app?.showDashboard(); };
window.showProfile = function() { 
    window.app?.showToast('Profile page coming soon!', 'info'); 
    document.getElementById('userDropdown')?.classList.add('hidden'); 
};
window.showOrders = function() { 
    window.app?.showToast('Orders page coming soon!', 'info'); 
    document.getElementById('userDropdown')?.classList.add('hidden'); 
};
window.showSellerDashboard = function() { window.app?.showSellerDashboard(); };
window.showAdminPanel = function() { window.app?.showAdminPanel(); };
window.toggleUserDropdown = function() { window.app?.toggleUserDropdown(); };
window.toggleCart = function() { window.app?.toggleCart(); };
window.proceedToCheckout = function() { window.app?.proceedToCheckout(); };
window.handleCheckout = function(event) { window.app?.handleCheckout(event); };
window.showCreateListing = function() { window.app?.showCreateListing(); };
window.handleCreateListing = function(event) { window.app?.handleCreateListing(event); };
window.logout = function() { window.app?.logout(); };

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (!window.app) {
        window.app = new CouponHubApp();
    }
});

// Fallback initialization if DOMContentLoaded already fired
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.app) {
            window.app = new CouponHubApp();
        }
    });
} else {
    if (!window.app) {
        window.app = new CouponHubApp();
    }
}
